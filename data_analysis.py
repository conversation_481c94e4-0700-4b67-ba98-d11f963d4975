#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集详细分析脚本
用于分析工作目录中的所有数据集，评估数据质量和可视化潜力
"""

import pandas as pd
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

def analyze_dataset(file_path, file_name):
    """
    分析单个数据集
    """
    print(f"\n{'='*60}")
    print(f"数据集分析：{file_name}")
    print(f"{'='*60}")
    
    try:
        # 读取数据
        df = pd.read_excel(file_path)
        
        # 基本信息
        print(f"📊 基本信息:")
        print(f"   数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")
        print(f"   内存使用: {df.memory_usage(deep=True).sum() / 1024:.2f} KB")
        
        # 数据类型
        print(f"\n📋 数据类型:")
        for col, dtype in df.dtypes.items():
            print(f"   {col}: {dtype}")
        
        # 缺失值统计
        print(f"\n🔍 缺失值统计:")
        missing_stats = df.isnull().sum()
        missing_percent = (missing_stats / len(df)) * 100
        for col in df.columns:
            if missing_stats[col] > 0:
                print(f"   {col}: {missing_stats[col]} ({missing_percent[col]:.1f}%)")
            else:
                print(f"   {col}: 无缺失值")
        
        # 数值型列的基本统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"\n📈 数值型列统计:")
            stats = df[numeric_cols].describe()
            print(stats.round(2))
        
        # 分类型列的统计
        categorical_cols = df.select_dtypes(include=['object']).columns
        if len(categorical_cols) > 0:
            print(f"\n📊 分类型列统计:")
            for col in categorical_cols:
                unique_count = df[col].nunique()
                print(f"   {col}: {unique_count} 个唯一值")
                if unique_count <= 10:
                    print(f"      值分布: {dict(df[col].value_counts().head())}")
        
        # 数据质量评估
        print(f"\n✅ 数据质量评估:")
        completeness = (1 - df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100
        print(f"   完整性: {completeness:.1f}%")
        
        # 重复行检查
        duplicates = df.duplicated().sum()
        print(f"   重复行: {duplicates} 行 ({duplicates/len(df)*100:.1f}%)")
        
        # 可视化潜力评估
        print(f"\n🎨 可视化潜力评估:")
        print(f"   数值型变量: {len(numeric_cols)} 个")
        print(f"   分类型变量: {len(categorical_cols)} 个")
        print(f"   时间序列潜力: {'是' if any('日期' in col or '时间' in col for col in df.columns) else '否'}")
        print(f"   地理信息潜力: {'是' if any(geo_word in str(df.columns) for geo_word in ['经度', '纬度', '地区', '区']) else '否'}")
        
        # 推荐的可视化类型
        print(f"\n💡 推荐可视化类型:")
        recommendations = []
        
        if len(numeric_cols) >= 2:
            recommendations.append("散点图（探索数值变量关系）")
        if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
            recommendations.append("条形图/柱状图（分类对比）")
        if any('日期' in col or '时间' in col for col in df.columns):
            recommendations.append("折线图（时间趋势）")
        if any(geo_word in str(df.columns) for geo_word in ['经度', '纬度']):
            recommendations.append("地理可视化（地图）")
        if len(numeric_cols) >= 1:
            recommendations.append("直方图/密度图（分布分析）")
            recommendations.append("箱形图（异常值检测）")
        
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
        
        return {
            'name': file_name,
            'shape': df.shape,
            'completeness': completeness,
            'numeric_cols': len(numeric_cols),
            'categorical_cols': len(categorical_cols),
            'has_time': any('日期' in col or '时间' in col for col in df.columns),
            'has_geo': any(geo_word in str(df.columns) for geo_word in ['经度', '纬度', '地区', '区']),
            'recommendations': recommendations,
            'duplicates': duplicates
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def main():
    """
    主函数：分析所有数据集
    """
    print("🔍 开始数据集分析...")
    
    data_dir = '数据可视化数据集-A'
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 获取所有Excel文件
    excel_files = [f for f in os.listdir(data_dir) if f.endswith('.xlsx')]
    
    if not excel_files:
        print("❌ 未找到Excel数据文件")
        return
    
    print(f"📁 找到 {len(excel_files)} 个数据集文件")
    
    # 分析结果存储
    analysis_results = []
    
    # 逐个分析数据集
    for file in excel_files:
        file_path = os.path.join(data_dir, file)
        result = analyze_dataset(file_path, file)
        if result:
            analysis_results.append(result)
    
    # 生成总结报告
    print(f"\n{'='*60}")
    print("📋 数据集分析总结")
    print(f"{'='*60}")
    
    print(f"总共分析了 {len(analysis_results)} 个数据集")
    
    # 按可视化潜力排序
    scored_datasets = []
    for result in analysis_results:
        score = 0
        score += result['numeric_cols'] * 2  # 数值型列权重高
        score += result['categorical_cols'] * 1.5  # 分类型列
        score += 3 if result['has_time'] else 0  # 时间序列加分
        score += 3 if result['has_geo'] else 0   # 地理信息加分
        score += (result['completeness'] / 100) * 2  # 完整性加分
        score -= result['duplicates'] * 0.1  # 重复行扣分
        
        scored_datasets.append((result, score))
    
    # 排序并推荐前3个
    scored_datasets.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🏆 推荐的前3个数据集（按可视化潜力排序）:")
    for i, (result, score) in enumerate(scored_datasets[:3], 1):
        print(f"\n{i}. {result['name']} (评分: {score:.1f})")
        print(f"   - 数据规模: {result['shape'][0]} 行 × {result['shape'][1]} 列")
        print(f"   - 数据完整性: {result['completeness']:.1f}%")
        print(f"   - 数值型变量: {result['numeric_cols']} 个")
        print(f"   - 分类型变量: {result['categorical_cols']} 个")
        print(f"   - 时间序列: {'是' if result['has_time'] else '否'}")
        print(f"   - 地理信息: {'是' if result['has_geo'] else '否'}")

if __name__ == "__main__":
    main()
