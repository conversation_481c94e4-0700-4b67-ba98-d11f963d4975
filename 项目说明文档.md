# 数据可视化期末考核项目说明文档

## 项目概述

本项目是一个完整的数据可视化分析项目，包含数据探索、图表创建和学术论文撰写三个主要阶段。项目基于Python生态系统，使用多种可视化库创建了9张高质量图表，并撰写了一篇符合学术规范的分析论文。

## 文件结构

```
项目根目录/
├── 数据可视化数据集-A/                    # 原始数据集目录
│   ├── 国内生产总值季度数据.xlsx
│   ├── 营销和产品销售表.xlsx
│   ├── 二手房数据.xlsx
│   └── [其他数据集文件...]
├── visualization_outputs/                 # 可视化图表输出目录
│   ├── GDP_折线图.png
│   ├── GDP_雷达图.png
│   ├── GDP_面积图.png
│   ├── 营销_散点图.png
│   ├── 营销_热力图.png
│   ├── 营销_箱形图.png
│   ├── 二手房_直方图.png
│   ├── 二手房_小提琴图.png
│   └── 二手房_气泡图.png
├── data_analysis.py                       # 数据探索分析脚本
├── visualization_charts.py                # 可视化图表生成脚本
├── 数据可视化分析论文.md                   # 学术论文（Markdown格式）
├── 项目说明文档.md                        # 本文档
└── README.md                              # 项目简介
```

## 技术栈

### 核心依赖库
- **pandas (2.2.3+)**: 数据处理和分析
- **numpy (1.24.3+)**: 数值计算
- **matplotlib (3.9.4+)**: 基础可视化
- **seaborn (0.13.2+)**: 统计可视化
- **plotly (6.1.2+)**: 交互式可视化
- **openpyxl (3.1.5+)**: Excel文件读取

### 开发环境
- **Python**: 3.9+
- **操作系统**: Windows/macOS/Linux
- **IDE**: VS Code/PyCharm/Jupyter Notebook

## 安装与运行

### 1. 环境准备

```bash
# 安装依赖库
pip install pandas numpy matplotlib seaborn plotly openpyxl

# 或使用requirements.txt（如果提供）
pip install -r requirements.txt
```

### 2. 数据探索分析

```bash
# 运行数据分析脚本
python data_analysis.py
```

**输出结果：**
- 控制台显示详细的数据分析报告
- 包含数据质量评估、统计信息和可视化潜力评分

### 3. 生成可视化图表

```bash
# 运行可视化脚本
python visualization_charts.py
```

**输出结果：**
- 在`visualization_outputs/`目录下生成9张PNG格式图表
- 图表分辨率：300 DPI
- 支持中文字体显示

### 4. 查看分析论文

直接打开`数据可视化分析论文.md`文件，使用支持Markdown的编辑器或查看器阅读。

## 项目特色

### 1. 数据集选择科学
- 基于数据质量、可视化潜力等多维度评估
- 涵盖经济、营销、房地产三个不同领域
- 数据完整性均达到100%

### 2. 可视化方法多样
- **时间序列分析**: 折线图、面积图
- **结构对比分析**: 雷达图、箱形图
- **关系探索分析**: 散点图、热力图、气泡图
- **分布特征分析**: 直方图、小提琴图

### 3. 技术实现规范
- 完整的中文字体支持
- 统一的图表样式和配色
- 高质量图像输出（300 DPI）
- 详细的代码注释

### 4. 学术论文完整
- 符合学术写作规范
- 包含完整的研究框架
- 深入的结果分析和讨论
- 约4,500字的详细内容

## 核心功能模块

### 1. 数据分析模块 (data_analysis.py)

**主要功能：**
- 自动化数据质量评估
- 多维度可视化潜力评分
- 数据集推荐排序
- 详细统计信息输出

**核心函数：**
```python
def analyze_dataset(file_path, file_name)  # 单个数据集分析
def main()                                 # 主分析流程
```

### 2. 可视化模块 (visualization_charts.py)

**主要功能：**
- 多类型图表自动生成
- 中文字体自动配置
- 高质量图像输出
- 数据预处理和清洗

**核心函数：**
```python
def create_gdp_charts(gdp_df)        # GDP数据可视化
def create_marketing_charts(marketing_df)  # 营销数据可视化
def create_housing_charts(housing_df)      # 房地产数据可视化
```

## 图表详细说明

### 1. 国内生产总值数据图表

#### GDP_折线图.png
- **类型**: 时间序列折线图
- **内容**: 2019-2022年GDP及各产业增加值趋势
- **特点**: 多条线对比，显示季节性特征

#### GDP_雷达图.png
- **类型**: 极坐标雷达图
- **内容**: 2022年Q4各产业结构对比
- **特点**: 多维度结构可视化

#### GDP_面积图.png
- **类型**: 堆叠面积图
- **内容**: 各产业增加值变化趋势
- **特点**: 显示绝对值和相对占比

### 2. 营销销售数据图表

#### 营销_散点图.png
- **类型**: 多维散点图（气泡图）
- **内容**: 营销费用与订单金额关系
- **特点**: 气泡大小和颜色表示额外维度

#### 营销_热力图.png
- **类型**: 相关性热力图
- **内容**: 9个营销指标相关性矩阵
- **特点**: 上三角遮罩，数值标注

#### 营销_箱形图.png
- **类型**: 多组箱形图
- **内容**: 5个关键指标标准化分布对比
- **特点**: 彩色填充，异常值显示

### 3. 二手房数据图表

#### 二手房_直方图.png
- **类型**: 2×2子图直方图组合
- **内容**: 单价、总价、面积、房龄分布
- **特点**: 多指标并列对比

#### 二手房_小提琴图.png
- **类型**: 多组小提琴图
- **内容**: 北京8个主要区域房价分布
- **特点**: 显示完整分布形态和统计量

#### 二手房_气泡图.png
- **类型**: 四维气泡图
- **内容**: 面积、房龄、总价、单价关系
- **特点**: 颜色和大小编码，图例说明

## 使用建议

### 1. 学习用途
- 适合数据可视化课程学习
- 可作为Python数据分析实践案例
- 学术论文写作参考

### 2. 项目扩展
- 可添加更多数据集和图表类型
- 可开发交互式可视化界面
- 可集成机器学习分析模块

### 3. 商业应用
- 可用于企业数据分析报告
- 可作为决策支持工具模板
- 可定制化开发特定行业解决方案

## 常见问题

### Q1: 中文字体显示异常怎么办？
**A1**: 确保系统安装了SimHei或Microsoft YaHei字体，或修改代码中的字体设置。

### Q2: 图表生成失败怎么办？
**A2**: 检查依赖库版本，确保数据文件路径正确，查看错误日志定位问题。

### Q3: 如何修改图表样式？
**A3**: 修改`visualization_charts.py`中的颜色、尺寸、标题等参数。

### Q4: 如何添加新的数据集？
**A4**: 在`load_datasets()`函数中添加新数据集加载代码，并创建对应的可视化函数。

## 技术支持

如有技术问题或改进建议，请通过以下方式联系：

- **项目文档**: 查看本说明文档和代码注释
- **学术论文**: 参考详细的方法论和分析过程
- **代码示例**: 参考现有实现进行扩展开发

## 版本信息

- **项目版本**: v1.0
- **创建时间**: 2024年12月
- **Python版本**: 3.9+
- **最后更新**: 2024年12月

## 许可证

本项目仅用于学术和教育目的，请遵守相关数据使用规范和版权要求。
