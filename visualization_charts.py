#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化图表生成脚本
为选定的3个数据集创建9张高质量可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
import os
from datetime import datetime

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')
sns.set_palette("husl")

# 创建输出目录
output_dir = 'visualization_outputs'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

def load_datasets():
    """
    加载选定的3个数据集
    """
    data_dir = '数据可视化数据集-A'
    
    # 加载国内生产总值数据
    gdp_df = pd.read_excel(os.path.join(data_dir, '国内生产总值季度数据.xlsx'))
    
    # 加载营销销售数据
    marketing_df = pd.read_excel(os.path.join(data_dir, '营销和产品销售表.xlsx'))
    
    # 加载二手房数据
    housing_df = pd.read_excel(os.path.join(data_dir, '二手房数据.xlsx'))
    
    return gdp_df, marketing_df, housing_df

def create_gdp_charts(gdp_df):
    """
    为国内生产总值数据创建3张图表：折线图、雷达图、面积图
    """
    print("🎨 正在创建国内生产总值数据可视化图表...")
    
    # 数据预处理：转换为时间序列格式
    gdp_melted = gdp_df.melt(id_vars=['指标'], var_name='季度', value_name='数值')
    
    # 1. 折线图 - GDP时间趋势
    plt.figure(figsize=(14, 8))
    
    for indicator in gdp_df['指标'].unique():
        data = gdp_melted[gdp_melted['指标'] == indicator]
        plt.plot(range(len(data)), data['数值'], marker='o', linewidth=2.5, 
                label=indicator, markersize=6)
    
    plt.title('中国GDP及各产业增加值季度变化趋势 (2019-2022)', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度（时间顺序）', fontsize=12)
    plt.ylabel('数值（亿元）', fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.xticks(range(0, 16, 2), [f'Q{i//4+1}-{2019+i//4}' for i in range(0, 16, 2)], rotation=45)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/GDP_折线图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 雷达图 - 各产业结构对比（最新季度）
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # 选择最新季度数据（2022年第四季度）
    latest_data = gdp_df['2022年第四季度'].values[1:]  # 排除总GDP，只看三个产业
    categories = ['第一产业', '第二产业', '第三产业']
    
    # 计算角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    # 数据闭合
    latest_data = np.append(latest_data, latest_data[0])
    
    ax.plot(angles, latest_data, 'o-', linewidth=2, color='#1f77b4')
    ax.fill(angles, latest_data, alpha=0.25, color='#1f77b4')
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=12)
    ax.set_title('2022年第四季度各产业增加值结构\n（雷达图）', fontsize=14, fontweight='bold', pad=30)
    ax.grid(True)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/GDP_雷达图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 面积图 - 各产业占比变化
    plt.figure(figsize=(14, 8))

    # 准备数据：计算各产业占总GDP的比例
    quarters = gdp_df.columns[1:]  # 所有季度列
    first_industry = gdp_df.iloc[1, 1:].values.astype(float)  # 第一产业
    second_industry = gdp_df.iloc[2, 1:].values.astype(float)  # 第二产业
    third_industry = gdp_df.iloc[3, 1:].values.astype(float)   # 第三产业

    x = np.array(range(len(quarters)))

    plt.stackplot(x, first_industry, second_industry, third_industry,
                 labels=['第一产业', '第二产业', '第三产业'],
                 alpha=0.8, colors=['#ff9999', '#66b3ff', '#99ff99'])
    
    plt.title('中国各产业增加值变化趋势 (2019-2022)\n（面积图）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度', fontsize=12)
    plt.ylabel('增加值（亿元）', fontsize=12)
    plt.legend(loc='upper left')
    plt.xticks(range(0, len(quarters), 2), 
               [quarters[i].replace('年第', 'Q').replace('季度', '') for i in range(0, len(quarters), 2)], 
               rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/GDP_面积图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ GDP数据图表创建完成")

def create_marketing_charts(marketing_df):
    """
    为营销销售数据创建3张图表：散点图、热力图、箱形图
    """
    print("🎨 正在创建营销销售数据可视化图表...")
    
    # 1. 散点图 - 营销费用与订单金额关系
    plt.figure(figsize=(12, 8))
    
    scatter = plt.scatter(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'], 
                         s=marketing_df['点击量']/10, alpha=0.6, 
                         c=marketing_df['下单新客数'], cmap='viridis')
    
    plt.title('营销费用与订单金额关系分析\n（气泡大小表示点击量，颜色表示新客数）', 
              fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('营销费用（元）', fontsize=12)
    plt.ylabel('订单金额（元）', fontsize=12)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('下单新客数', fontsize=10)
    
    # 添加趋势线
    z = np.polyfit(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'], 1)
    p = np.poly1d(z)
    plt.plot(marketing_df['营销费用（元）'], p(marketing_df['营销费用（元）']), 
             "r--", alpha=0.8, linewidth=2, label=f'趋势线 (R²={np.corrcoef(marketing_df["营销费用（元）"], marketing_df["订单金额（元）"])[0,1]**2:.3f})')
    
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/营销_散点图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 热力图 - 营销指标相关性矩阵
    plt.figure(figsize=(12, 10))
    
    # 选择数值型列进行相关性分析
    numeric_cols = ['营销费用（元）', '展现量', '点击量', '订单金额（元）', 
                   '加购数', '下单新客数', '访问页面数', '进店数', '商品关注数']
    
    correlation_matrix = marketing_df[numeric_cols].corr()
    
    # 创建热力图
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r', 
                center=0, square=True, fmt='.2f', cbar_kws={"shrink": .8})
    
    plt.title('营销指标相关性热力图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/营销_热力图.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 箱形图 - 不同指标的分布对比
    plt.figure(figsize=(14, 8))

    # 标准化数据以便比较
    normalized_data = []
    labels = []
    for col in ['展现量', '点击量', '订单金额（元）', '加购数', '下单新客数']:
        normalized_values = (marketing_df[col] - marketing_df[col].mean()) / marketing_df[col].std()
        normalized_data.append(normalized_values)
        labels.append(col)

    box_plot = plt.boxplot(normalized_data, labels=labels, patch_artist=True)

    # 设置颜色
    colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink']
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)

    plt.title('营销关键指标标准化分布对比\n（箱形图）', fontsize=14, fontweight='bold', pad=20)
    plt.ylabel('标准化数值', fontsize=12)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/营销_箱形图.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ 营销数据图表创建完成")

def create_housing_charts(housing_df):
    """
    为二手房数据创建3张图表：直方图、小提琴图、气泡图
    """
    print("🎨 正在创建二手房数据可视化图表...")
    
    # 1. 直方图 - 房价分布
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('北京二手房关键指标分布分析', fontsize=16, fontweight='bold')
    
    # 单价分布
    axes[0,0].hist(housing_df['单价（元/平方米）'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_title('单价分布', fontsize=12, fontweight='bold')
    axes[0,0].set_xlabel('单价（元/平方米）')
    axes[0,0].set_ylabel('频数')
    axes[0,0].grid(True, alpha=0.3)
    
    # 总价分布
    axes[0,1].hist(housing_df['总价（万元）'], bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0,1].set_title('总价分布', fontsize=12, fontweight='bold')
    axes[0,1].set_xlabel('总价（万元）')
    axes[0,1].set_ylabel('频数')
    axes[0,1].grid(True, alpha=0.3)
    
    # 面积分布
    axes[1,0].hist(housing_df['面积（平方米）'], bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1,0].set_title('面积分布', fontsize=12, fontweight='bold')
    axes[1,0].set_xlabel('面积（平方米）')
    axes[1,0].set_ylabel('频数')
    axes[1,0].grid(True, alpha=0.3)
    
    # 房龄分布
    axes[1,1].hist(housing_df['房龄（年）'], bins=30, alpha=0.7, color='gold', edgecolor='black')
    axes[1,1].set_title('房龄分布', fontsize=12, fontweight='bold')
    axes[1,1].set_xlabel('房龄（年）')
    axes[1,1].set_ylabel('频数')
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/二手房_直方图.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 小提琴图 - 各区域房价分布对比
    plt.figure(figsize=(16, 10))

    # 选择房源数量较多的区域进行对比
    top_districts = housing_df['所在区'].value_counts().head(8).index
    filtered_data = housing_df[housing_df['所在区'].isin(top_districts)]

    # 创建小提琴图
    violin_parts = plt.violinplot([filtered_data[filtered_data['所在区'] == district]['单价（元/平方米）'].values
                                  for district in top_districts],
                                 positions=range(len(top_districts)),
                                 showmeans=True, showmedians=True)

    # 设置颜色
    colors = plt.cm.Set3(np.linspace(0, 1, len(top_districts)))
    for pc, color in zip(violin_parts['bodies'], colors):
        pc.set_facecolor(color)
        pc.set_alpha(0.7)

    plt.title('北京各区二手房单价分布对比\n（小提琴图）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('行政区', fontsize=12)
    plt.ylabel('单价（元/平方米）', fontsize=12)
    plt.xticks(range(len(top_districts)), top_districts, rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/二手房_小提琴图.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 气泡图 - 面积、房龄与价格关系
    plt.figure(figsize=(14, 10))

    # 为了更好的可视化效果，对数据进行采样
    sample_data = housing_df.sample(n=min(1000, len(housing_df)), random_state=42)

    # 创建气泡图
    scatter = plt.scatter(sample_data['面积（平方米）'], sample_data['房龄（年）'],
                         s=sample_data['总价（万元）']/5,  # 气泡大小表示总价
                         c=sample_data['单价（元/平方米）'],  # 颜色表示单价
                         alpha=0.6, cmap='viridis', edgecolors='black', linewidth=0.5)

    plt.title('北京二手房面积、房龄与价格关系分析\n（气泡大小=总价，颜色=单价）',
              fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('面积（平方米）', fontsize=12)
    plt.ylabel('房龄（年）', fontsize=12)

    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('单价（元/平方米）', fontsize=10)

    # 添加图例说明气泡大小
    bubble_sizes = [100, 500, 1000]
    bubble_labels = ['100万', '500万', '1000万']
    legend_elements = []
    for size, label in zip(bubble_sizes, bubble_labels):
        legend_elements.append(plt.scatter([], [], s=size/5, c='gray', alpha=0.6,
                                         edgecolors='black', linewidth=0.5, label=label))

    plt.legend(handles=legend_elements, title='总价', loc='upper right',
              bbox_to_anchor=(1.15, 1))

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/二手房_气泡图.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ 二手房数据图表创建完成")

def main():
    """
    主函数：执行所有可视化图表创建
    """
    print("🚀 开始创建数据可视化图表...")
    print(f"📁 输出目录: {output_dir}")
    
    # 加载数据集
    gdp_df, marketing_df, housing_df = load_datasets()
    
    # 创建各数据集的图表
    create_gdp_charts(gdp_df)
    create_marketing_charts(marketing_df)
    create_housing_charts(housing_df)
    
    print(f"\n🎉 所有图表创建完成！")
    print(f"📊 共生成了9张高质量可视化图表，保存在 {output_dir} 目录中")
    print(f"📈 图表分辨率: 300 DPI")
    print(f"🎨 支持中文显示")

if __name__ == "__main__":
    main()
