# 数据可视化期末考核项目

## 🎯 项目简介

这是一个完整的数据可视化分析项目，包含数据探索、图表创建和学术论文撰写。项目基于Python，使用多种可视化技术对中国经济、营销业务和房地产市场数据进行深入分析。

## 📊 项目成果

### 数据集分析
- ✅ 分析了8个候选数据集
- ✅ 选择了3个最优数据集
- ✅ 完成了全面的数据质量评估

### 可视化图表
- ✅ 创建了9张高质量图表（300 DPI）
- ✅ 支持中文字体显示
- ✅ 涵盖多种图表类型

### 学术论文
- ✅ 撰写了4,500字学术论文
- ✅ 符合学术写作规范
- ✅ 包含完整的分析框架

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install pandas numpy matplotlib seaborn plotly openpyxl
```

### 2. 运行数据分析
```bash
python data_analysis.py
```

### 3. 生成可视化图表
```bash
python visualization_charts.py
```

### 4. 查看分析论文
打开 `数据可视化分析论文.md` 文件

## 📁 文件结构

```
├── 数据可视化数据集-A/           # 原始数据集
├── visualization_outputs/       # 生成的图表
├── data_analysis.py             # 数据分析脚本
├── visualization_charts.py      # 可视化脚本
├── 数据可视化分析论文.md         # 学术论文
├── 项目说明文档.md              # 详细说明
└── README.md                   # 本文件
```

## 📈 图表展示

### 国内生产总值数据 (3张图表)
- 📊 **折线图**: GDP时间趋势分析
- 🎯 **雷达图**: 产业结构对比
- 📈 **面积图**: 产业变化趋势

### 营销销售数据 (3张图表)
- 🔍 **散点图**: 投入产出关系分析
- 🌡️ **热力图**: 指标相关性分析
- 📦 **箱形图**: 指标分布对比

### 二手房数据 (3张图表)
- 📊 **直方图**: 房价分布特征
- 🎻 **小提琴图**: 区域价格对比
- 💫 **气泡图**: 多维关系分析

## 🛠️ 技术栈

- **Python 3.9+**
- **Pandas**: 数据处理
- **Matplotlib**: 基础可视化
- **Seaborn**: 统计可视化
- **Plotly**: 交互式图表
- **NumPy**: 数值计算

## 📋 主要特色

- ✨ **科学的数据集选择**: 基于多维度评估选择最优数据集
- 🎨 **多样的可视化方法**: 涵盖9种不同类型的图表
- 🔧 **规范的技术实现**: 完整的中文支持和高质量输出
- 📚 **完整的学术论文**: 符合学术规范的深度分析

## 📖 详细文档

查看 `项目说明文档.md` 获取：
- 详细的安装和运行指南
- 完整的技术架构说明
- 图表类型和功能介绍
- 常见问题解答

## 🎓 适用场景

- 📚 **教育学习**: 数据可视化课程实践
- 💼 **商业分析**: 企业数据分析报告
- 🔬 **学术研究**: 可视化方法论研究
- 🛠️ **技能提升**: Python数据分析技能

## 📊 数据集信息

| 数据集 | 规模 | 完整性 | 特点 |
|--------|------|--------|------|
| 国内生产总值季度数据 | 4×17 | 100% | 时间序列，经济指标 |
| 营销和产品销售数据 | 28×10 | 100% | 营销漏斗，关联分析 |
| 北京二手房数据 | 2909×7 | 100% | 大样本，地理信息 |

## 🏆 项目亮点

1. **数据驱动**: 基于真实数据的深度分析
2. **方法多样**: 9种可视化方法的综合应用
3. **质量保证**: 300 DPI高质量图表输出
4. **学术规范**: 完整的论文写作和引用规范
5. **代码规范**: 详细注释和模块化设计

## 📞 技术支持

如有问题或建议，请参考：
- 📖 项目说明文档
- 📝 代码注释
- 📄 学术论文方法论部分

---

**项目完成时间**: 2024年12月  
**版本**: v1.0  
**用途**: 学术教育
